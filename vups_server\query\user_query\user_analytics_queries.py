"""
Analytics and aggregation module.
Handles top content queries, sentiment analysis, AI-generated data, and complex aggregations.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from vups.logger import logger
import vups.utils as U
from vups_server.base.query_base import BaseQueryService


class UserAnalyticsService(BaseQueryService):
    """Service for analytics, aggregations, and AI-generated data queries."""

    def __init__(self):
        super().__init__(cache_ttl=1800)  # 30 minutes cache for analytics

    async def get_top_comments(self, uid: str, start_date: str = None, end_date: str = None, limit: int = 10, source: str = "all") -> List[Dict]:
        """
        Query top comments for a user with enhanced caching and optional date/source filtering.

        Args:
            uid: User UID
            start_date: Start date for filtering (YYYY-MM-DD format), optional
            end_date: End date for filtering (YYYY-MM-DD format), optional
            limit: Maximum number of comments to return
            source: Source filter ("video", "dynamic", "all")

        Returns:
            List of top comments sorted by like count
        """
        # Handle both old signature (uid, limit) and new signature (uid, start_date, end_date, limit, source)
        if isinstance(start_date, int) and end_date is None:
            # Old signature: get_top_comments(uid, limit)
            limit = start_date
            start_date = None
            end_date = None
            source = "all"

        cache_key = f"top_comments_{uid}_{start_date}_{end_date}_{limit}_{source}"

        # Try cache first with longer TTL for top comments
        cached_result = await self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        video_table = f"video_comment_{uid}"
        dynamics_table = f"dynamics_comment_{uid}"

        # Check if tables exist and source filter
        video_exists = await self._check_table_exists(video_table) and source in ["video", "all"]
        dynamics_exists = await self._check_table_exists(dynamics_table) and source in ["dynamic", "all"]

        all_comments = []

        # Build date filter conditions
        date_filter = ""
        date_params = []
        if start_date and end_date:
            from datetime import datetime, timedelta
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            date_filter = " AND comment_time >= $2 AND comment_time < $3"
            date_params = [start_dt, end_dt]

        # Get video comments
        if video_exists:
            video_query = f"""
                SELECT 'video' as source, bvid as content_id, comment_id,
                       comment_content, like_num, reply_num, comment_time,
                       comment_user_name, comment_user_uid, comment_user_face
                FROM {video_table}
                WHERE comment_content IS NOT NULL
                AND like_num > 0{date_filter}
                ORDER BY like_num DESC
                LIMIT $1
            """

            params = [limit * 2] + date_params
            video_results = await self._execute_query(
                video_query, params, "fetch"
            )

            if video_results:
                all_comments.extend(video_results)

        # Get dynamics comments
        if dynamics_exists:
            dynamics_query = f"""
                SELECT 'dynamic' as source, dynamic_id as content_id, comment_id,
                       comment_content, like_num, reply_num, comment_time,
                       comment_user_name, comment_user_uid, comment_user_face
                FROM {dynamics_table}
                WHERE comment_content IS NOT NULL
                AND like_num > 0{date_filter}
                ORDER BY like_num DESC
                LIMIT $1
            """

            params = [limit * 2] + date_params
            dynamics_results = await self._execute_query(
                dynamics_query, params, "fetch"
            )

            if dynamics_results:
                all_comments.extend(dynamics_results)

        # Sort all comments by like count and take top N
        all_comments.sort(key=lambda x: x["like_num"], reverse=True)
        top_comments = all_comments[:limit]

        # Format results
        formatted_comments = []
        for comment in top_comments:
            formatted_comments.append({
                "source": comment["source"],
                "content_id": comment["content_id"],
                "comment_id": comment["comment_id"],
                "content": comment["comment_content"],
                "like_num": comment["like_num"],
                "reply_num": comment["reply_num"],
                "time": comment["comment_time"],
                "user_name": comment["comment_user_name"],
                "user_uid": comment["comment_user_uid"],
                "user_face": comment["comment_user_face"],
            })

        # Cache with longer TTL for top comments
        await self.cache.set(cache_key, formatted_comments, ttl=3600)  # 1 hour

        return formatted_comments

    async def get_top_comment_users(self, uid: str, start_date: str = None, end_date: str = None, limit: int = 10, source: str = "all"):
        """
        Query top comment users for a user with enhanced caching and date/source filtering.

        Args:
            uid: User UID
            start_date: Start date for filtering (YYYY-MM-DD format), optional
            end_date: End date for filtering (YYYY-MM-DD format), optional
            limit: Maximum number of users to return
            source: Source filter ("video", "dynamic", "all")

        Returns:
            Tuple of three lists: (top_likes_users, top_comments_users, top_replies_users)
        """
        # Handle both old signature (uid, limit) and new signature (uid, start_date, end_date, limit, source)
        if isinstance(start_date, int) and end_date is None:
            # Old signature: get_top_comment_users(uid, limit)
            limit = start_date
            start_date = None
            end_date = None
            source = "all"

        cache_key = f"top_comment_users_{uid}_{start_date}_{end_date}_{limit}_{source}"

        # Try cache first
        cached_result = await self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        video_table = f"video_comment_{uid}"
        dynamics_table = f"dynamics_comment_{uid}"

        # Check if tables exist and source filter
        video_exists = await self._check_table_exists(video_table) and source in ["video", "all"]
        dynamics_exists = await self._check_table_exists(dynamics_table) and source in ["dynamic", "all"]

        user_stats = {}

        # Build date filter conditions
        date_filter = ""
        date_params = []
        if start_date and end_date:
            from datetime import datetime, timedelta
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            date_filter = " AND comment_time >= $2 AND comment_time < $3"
            date_params = [start_dt, end_dt]

        # Aggregate video comment stats
        if video_exists:
            video_query = f"""
                SELECT comment_user_uid, comment_user_name, comment_user_face,
                       SUM(like_num) as total_likes, COUNT(*) as comment_count,
                       SUM(reply_num) as total_replies
                FROM {video_table}
                WHERE comment_user_uid IS NOT NULL
                AND comment_user_name IS NOT NULL{date_filter}
                GROUP BY comment_user_uid, comment_user_name, comment_user_face
                ORDER BY total_likes DESC
                LIMIT $1
            """

            params = [limit * 2] + date_params
            video_results = await self._execute_query(
                video_query, params, "fetch"
            )

            if video_results:
                for row in video_results:
                    uid_key = row["comment_user_uid"]
                    if uid_key not in user_stats:
                        user_stats[uid_key] = {
                            "uid": uid_key,
                            "name": row["comment_user_name"],
                            "face": row["comment_user_face"],
                            "total_likes": 0,
                            "comment_count": 0,
                            "total_replies": 0,
                        }
                    user_stats[uid_key]["total_likes"] += row["total_likes"] or 0
                    user_stats[uid_key]["comment_count"] += row["comment_count"] or 0
                    user_stats[uid_key]["total_replies"] += row["total_replies"] or 0

        # Aggregate dynamics comment stats
        if dynamics_exists:
            dynamics_query = f"""
                SELECT comment_user_uid, comment_user_name, comment_user_face,
                       SUM(like_num) as total_likes, COUNT(*) as comment_count,
                       SUM(reply_num) as total_replies
                FROM {dynamics_table}
                WHERE comment_user_uid IS NOT NULL
                AND comment_user_name IS NOT NULL{date_filter}
                GROUP BY comment_user_uid, comment_user_name, comment_user_face
                ORDER BY total_likes DESC
                LIMIT $1
            """

            params = [limit * 2] + date_params
            dynamics_results = await self._execute_query(
                dynamics_query, params, "fetch"
            )

            if dynamics_results:
                for row in dynamics_results:
                    uid_key = row["comment_user_uid"]
                    if uid_key not in user_stats:
                        user_stats[uid_key] = {
                            "uid": uid_key,
                            "name": row["comment_user_name"],
                            "face": row["comment_user_face"],
                            "total_likes": 0,
                            "comment_count": 0,
                            "total_replies": 0,
                        }
                    user_stats[uid_key]["total_likes"] += row["total_likes"] or 0
                    user_stats[uid_key]["comment_count"] += row["comment_count"] or 0
                    user_stats[uid_key]["total_replies"] += row["total_replies"] or 0

        # Create three separate lists as expected by the API
        # Top likes users
        top_likes_users = sorted(
            user_stats.values(),
            key=lambda x: x["total_likes"],
            reverse=True
        )[:limit]
        top_likes_list = [
            {
                "name": user["name"],
                "likeNum": user["total_likes"],
                "uid": user["uid"],
                "face": user["face"],
            }
            for user in top_likes_users
        ]

        # Top comments users
        top_comments_users = sorted(
            user_stats.values(),
            key=lambda x: x["comment_count"],
            reverse=True
        )[:limit]
        top_comments_list = [
            {
                "name": user["name"],
                "appealNum": user["comment_count"],
                "uid": user["uid"],
                "face": user["face"],
            }
            for user in top_comments_users
        ]

        # Top replies users
        top_replies_users = sorted(
            user_stats.values(),
            key=lambda x: x["total_replies"],
            reverse=True
        )[:limit]
        top_replies_list = [
            {
                "name": user["name"],
                "rcountSum": user["total_replies"],
                "uid": user["uid"],
                "face": user["face"],
            }
            for user in top_replies_users
        ]

        result_tuple = (top_likes_list, top_comments_list, top_replies_list)

        # Cache with longer TTL
        await self.cache.set(cache_key, result_tuple, ttl=3600)  # 1 hour

        return result_tuple

    async def get_top_videos(self, uid: str, start_date: str = None, end_date: str = None, limit: int = 10) -> List[Dict]:
        """
        Query top videos for a user based on heat score with optional date filtering.

        Args:
            uid: User UID
            start_date: Start date for filtering (YYYY-MM-DD format), optional
            end_date: End date for filtering (YYYY-MM-DD format), optional
            limit: Maximum number of videos to return

        Returns:
            List of top videos sorted by heat
        """
        # Handle both old signature (uid, limit) and new signature (uid, start_date, end_date, limit)
        if isinstance(start_date, int) and end_date is None:
            # Old signature: get_top_videos(uid, limit)
            limit = start_date
            start_date = None
            end_date = None

        cache_key = f"top_videos_{uid}_{start_date}_{end_date}_{limit}"

        # Build query with optional date filtering
        query_parts = ["""
            SELECT bvid, video_name, description, cover, datetime, play_num,
                   comment_num, like_num, coin, favorite_num, share_num,
                   danmuku_num, aid, length, honor_short, honor_count, honor,
                   video_ai_conclusion, heat
            FROM videos_table
            WHERE uid = $1"""]

        params = [uid]
        param_count = 1

        if start_date and end_date:
            from datetime import datetime, timedelta
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            param_count += 2
            query_parts.append(f" AND datetime >= ${param_count-1} AND datetime < ${param_count}")
            params.extend([start_dt, end_dt])

        query_parts.append(" ORDER BY heat DESC, datetime DESC")
        param_count += 1
        query_parts.append(f" LIMIT ${param_count}")
        params.append(limit)

        query = "".join(query_parts)

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=params,
            fetch_type="fetch"
        )

        videos = []
        if results:
            for row in results:
                videos.append({
                    "bvid": row["bvid"],
                    "name": row["video_name"],
                    "description": row["description"],
                    "face": row["cover"],
                    "heat": row["heat"],
                    "play_num": row["play_num"],
                    "comment_num": row["comment_num"],
                    "like_num": row["like_num"],
                    "coin_num": row["coin"],
                    "favorite_num": row["favorite_num"],
                    "danmaku_num": row["danmuku_num"],
                    "share_num": row["share_num"],
                    "aid": row["aid"],
                    "length": row["length"],
                    "pubtime": (
                        row["datetime"].strftime("%Y-%m-%d")
                        if row["datetime"]
                        else None
                    ),
                    "honor_short": (
                        str(row["honor_short"]) if row["honor_short"] else ""
                    ),
                    "honor": row["honor"],
                    "honor_count": row["honor_count"],
                    "video_ai_conclusion": row["video_ai_conclusion"],
                })

        return videos

    async def get_top_dynamics(self, uid: str, limit: int = 10) -> List[Dict]:
        """
        Query top dynamics for a user based on heat score.

        Args:
            uid: User UID
            limit: Maximum number of dynamics to return

        Returns:
            List of top dynamics sorted by heat
        """
        cache_key = f"top_dynamics_{uid}_{limit}"

        query = """
            SELECT name, timestamp, datetime, dynamic_content, url, topic,
                   dynamic_id, share_num, comment_num, like_num, comment_id,
                   comment_type, heat
            FROM dynamics_table
            WHERE uid = $1
            ORDER BY heat DESC, datetime DESC
            LIMIT $2
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, limit],
            fetch_type="fetch"
        )

        dynamics = []
        if results:
            for row in results:
                dynamics.append({
                    "id": row["comment_id"],
                    "name": row["name"],
                    "content": row["dynamic_content"],
                    "topic": row["topic"],
                    "url": row["url"],
                    "heat": row["heat"],
                    "pubtime": (
                        row["datetime"].strftime("%Y-%m-%d")
                        if row["datetime"]
                        else None
                    ),
                    "share_num": row["share_num"],
                    "comment_num": row["comment_num"],
                    "like_num": row["like_num"],
                })

        return dynamics

    async def _check_table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the database.

        Args:
            table_name: Name of the table to check

        Returns:
            True if table exists, False otherwise
        """
        query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = $1
            )
        """

        result = await self._execute_query(query, [table_name], "fetchval")
        return result is True

    async def get_recent_relationships(self, uid: str, limit: int = 10) -> List[Dict]:
        """
        Query recent relationships from AI-generated data.

        Args:
            uid: User UID
            limit: Maximum number of relationships to return

        Returns:
            List of recent relationships
        """
        cache_key = f"recent_relationships_{uid}_{limit}"

        query = """
            SELECT uid, name, relationship, datetime
            FROM ai_gen_table
            WHERE uid = $1 AND relationship IS NOT NULL
            ORDER BY datetime DESC
            LIMIT $1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, limit],
            fetch_type="fetch"
        )

        relationships = []
        if result:
            relationships = result["relationships"]

        return relationships

    async def get_tieba_summaries(self, uid: str, limit: int = 5) -> List[Dict]:
        """
        Query tieba summaries from AI-generated data.

        Args:
            uid: User UID
            limit: Maximum number of summaries to return

        Returns:
            List of tieba summaries
        """
        cache_key = f"tieba_summaries_{uid}_{limit}"

        query = """
            SELECT uid, name, tieba_summary, datetime
            FROM ai_gen_table
            WHERE uid = $1 AND tieba_summary IS NOT NULL
            ORDER BY datetime DESC
            LIMIT $2
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, limit],
            fetch_type="fetch"
        )

        summaries = []
        if results:
            for row in results:
                summaries.append({
                    "uid": row["uid"],
                    "name": row["name"],
                    "summary": row["tieba_summary"],
                    "datetime": (
                        row["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                        if row["datetime"]
                        else None
                    ),
                })

        return summaries

    async def get_rise_reasons(self, uid: str, limit: int = 5) -> List[Dict]:
        """
        Query rise reasons from AI-generated data.

        Args:
            uid: User UID
            limit: Maximum number of rise reasons to return

        Returns:
            List of rise reasons
        """
        cache_key = f"rise_reasons_{uid}_{limit}"

        query = """
            SELECT uid, name, rise_reason, datetime
            FROM ai_gen_table
            WHERE uid = $1 AND rise_reason IS NOT NULL
            ORDER BY datetime DESC
            LIMIT $2
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, limit],
            fetch_type="fetch"
        )

        reasons = []
        if results:
            for row in results:
                reasons.append({
                    "uid": row["uid"],
                    "name": row["name"],
                    "reason": row["rise_reason"],
                    "datetime": (
                        row["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                        if row["datetime"]
                        else None
                    ),
                })

        return reasons

    async def get_video_ai_conclusion(self, bvid: str) -> Optional[str]:
        """
        Query AI conclusion for a specific video.

        Args:
            bvid: Video BVID

        Returns:
            AI conclusion text or None if not found
        """
        cache_key = f"video_ai_conclusion_{bvid}"

        query = """
            SELECT video_ai_conclusion
            FROM videos_table
            WHERE bvid = $1
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[bvid],
            fetch_type="fetchval"
        )

        return result

    async def get_fans_medal_rank(
        self,
        uid: str,
        target_datetime: Optional[datetime] = None
    ) -> List[Dict]:
        """
        Query fans medal rank data.

        Args:
            uid: User UID
            target_datetime: Specific datetime to query (defaults to latest)

        Returns:
            List of fans medal rank data
        """
        if target_datetime:
            cache_key = f"fans_medal_rank_{uid}_{target_datetime.isoformat()}"
            query = """
                SELECT uid, name, fans_medal_rank, datetime
                FROM fans_medal_rank_table
                WHERE uid = $1 AND datetime <= $2
                ORDER BY datetime DESC
                LIMIT 1
            """
            params = [uid, target_datetime]
        else:
            cache_key = f"fans_medal_rank_latest_{uid}"
            query = """
                SELECT uid, name, fans_medal_rank, datetime
                FROM fans_medal_rank_table
                WHERE uid = $1
                ORDER BY datetime DESC
                LIMIT 1
            """
            params = [uid]

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=params,
            fetch_type="fetchrow"
        )

        if result:
            try:
                rank_data = json.loads(result["fans_medal_rank"]) if result["fans_medal_rank"] else []
                return rank_data
            except (json.JSONDecodeError, TypeError):
                logger.error(f"Failed to parse fans medal rank data for UID={uid}")
                return []

        return []

    async def get_recent_comments_sentiment(
        self,
        uid: str,
        recent_days: int = 30
    ) -> Optional[float]:
        """
        Query recent comments sentiment value.

        Args:
            uid: User UID
            recent_days: Number of recent days to analyze

        Returns:
            Average sentiment value or None if no data
        """
        cache_key = f"comments_sentiment_{uid}_{recent_days}"

        cutoff_date = datetime.now() - timedelta(days=recent_days)

        query = """
            SELECT AVG(sentiment_value) as avg_sentiment
            FROM comments_sentiment_table
            WHERE uid = $1 AND datetime >= $2
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, cutoff_date],
            fetch_type="fetchval"
        )

        return float(result) if result is not None else None


# Global instance for easy access
user_analytics_service = UserAnalyticsService()
